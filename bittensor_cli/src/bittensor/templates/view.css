/* ===================== Base Styles & Typography ===================== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Noto+Sans:wght@400;500;600&display=swap');

body {
    font-family: 'Inter', 'Noto Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Arial Unicode MS', sans-serif;
    margin: 0;
    padding: 24px;
    background: #000000;
    color: #ffffff;
}

input, button, select {
    font-family: inherit;
    font-feature-settings: normal;
}

/* ===================== Main Page Header ===================== */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.wallet-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.wallet-name {
    font-size: 1.1em;
    font-weight: 500;
    color: #FF9900;
}

.wallet-address-container {
    position: relative;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.wallet-address {
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.5);
    font-family: monospace;
    transition: color 0.2s ease;
}

.wallet-address-container:hover .wallet-address {
    color: rgba(255, 255, 255, 0.8);
}

.copy-indicator {
    background: rgba(255, 153, 0, 0.1);
    color: rgba(255, 153, 0, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7em;
    transition: all 0.2s ease;
    opacity: 0;
}

.wallet-address-container:hover .copy-indicator {
    opacity: 1;
    background: rgba(255, 153, 0, 0.2);
}

.wallet-address-container.copied .copy-indicator {
    opacity: 1;
    background: rgba(255, 153, 0, 0.3);
    color: #FF9900;
}

.stake-metrics {
    display: flex;
    gap: 24px;
    align-items: center;
}

.stake-metric {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
    position: relative;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.stake-metric:hover {
    background: rgba(255, 153, 0, 0.05);
}

.stake-metric .metric-label {
    font-size: 0.8em;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stake-metric .metric-value {
    font-size: 1.1em;
    font-weight: 500;
    color: #FF9900;
    font-feature-settings: "tnum";
    font-variant-numeric: tabular-nums;
}

.slippage-value {
    display: flex;
    align-items: center;
    gap: 6px;
}

.slippage-detail {
    font-size: 0.8em;
    color: rgba(255, 255, 255, 0.5);
}

/* ===================== Main Page Filters ===================== */
.filters-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 24px 0;
    gap: 16px;
}

.search-box input {
    padding: 10px 16px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.03);
    color: rgba(255, 255, 255, 0.7);
    width: 240px;
    font-size: 0.9em;
    transition: all 0.2s ease;
}
.search-box input::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.search-box input:focus {
    outline: none;
    border-color: rgba(255, 153, 0, 0.5);
    background: rgba(255, 255, 255, 0.06);
    color: rgba(255, 255, 255, 0.9);
}

.filter-toggles {
    display: flex;
    gap: 16px;
}

.filter-toggles label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    cursor: pointer;
    user-select: none;
}

/* Checkbox styling for both main page and subnet page */
.filter-toggles input[type="checkbox"],
.toggle-label input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 153, 0, 0.3);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.2);
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

.filter-toggles input[type="checkbox"]:hover,
.toggle-label input[type="checkbox"]:hover {
    border-color: #FF9900;
}

.filter-toggles input[type="checkbox"]:checked,
.toggle-label input[type="checkbox"]:checked {
    background: #FF9900;
    border-color: #FF9900;
}

.filter-toggles input[type="checkbox"]:checked::after,
.toggle-label input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid #000;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.filter-toggles label:hover,
.toggle-label:hover {
    color: rgba(255, 255, 255, 0.9);
}
.disabled-label {
    opacity: 0.5;
    cursor: not-allowed;
}
.add-stake-button {
    padding: 10px 20px;
    font-size: 0.8rem;
}
.export-csv-button {
    padding: 10px 20px;
    font-size: 0.8rem;
}
.button-group {
    display: flex;
    gap: 8px;
}

/* ===================== Main Page Subnet Table ===================== */
.subnets-table-container {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    overflow: hidden;
}

.subnets-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95em;
}

.subnets-table th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 500;
    text-align: left;
    padding: 16px;
    color: rgba(255, 255, 255, 0.7);
}

.subnets-table td {
    padding: 14px 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.subnet-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.subnet-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

.subnet-name {
    color: #ffffff;
    font-weight: 500;
    font-size: 0.95em;
}

.price, .market-cap, .your-stake, .emission {
    font-family: 'Inter', monospace;
    font-size: 1.0em;
    font-feature-settings: "tnum";
    font-variant-numeric: tabular-nums;
    letter-spacing: 0.01em;
    white-space: nowrap;
}

.stake-status {
    font-size: 0.85em;
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
}

.stake-status.staked {
    background: rgba(255, 153, 0, 0.1);
    color: #FF9900;
}

.subnets-table th.sortable {
    cursor: pointer;
    position: relative;
    padding-right: 20px;
}

.subnets-table th.sortable:hover {
    color: #FF9900;
}

.subnets-table th[data-sort] {
    color: #FF9900;
}

/* ===================== Subnet Tiles View ===================== */
.subnet-tiles-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
}

.subnet-tile {
    width: clamp(75px, 6vw, 600px);
    height: clamp(75px, 6vw, 600px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    font-size: clamp(0.6rem, 1vw, 1.4rem);
}

.tile-netuid {
    position: absolute;
    top: 0.4em;
    left: 0.4em;
    font-size: 0.7em;
    color: rgba(255, 255, 255, 0.6);
}

.tile-symbol {
    font-size: 1.6em;
    margin-bottom: 0.4em;
    color: #FF9900;
}

.tile-name {
    display: block;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1em;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0.4em;
}

.tile-market-cap {
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 2px;
}

.subnet-tile:hover {
    transform: translateY(-2px);
    box-shadow:
        0 0 12px rgba(255, 153, 0, 0.6),
        0 0 24px rgba(255, 153, 0, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.subnet-tile.staked {
    border: 1px solid rgba(255, 153, 0, 0.3);
}

.subnet-tile.staked::before {
    content: '';
    position: absolute;
    top: 0.4em;
    right: 0.4em;
    width: 0.5em;
    height: 0.5em;
    border-radius: 50%;
    background: #FF9900;
}

/* ===================== Subnet Detail Page Header ===================== */
.subnet-header {
    padding: 16px;
    border-radius: 12px;
    margin-bottom: 0px;
}

.subnet-header h2 {
    margin: 0;
    font-size: 1.3em;
}

.subnet-price {
    font-size: 1.3em;
    color: #FF9900;
}

.subnet-title-row {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    align-items: start;
    margin: 0;
    position: relative;
    min-height: 60px;
}

.title-price {
    grid-column: 1;
    padding-top: 0;
    margin-top: -10px;
}

.header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 16px;
}

.toggle-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    cursor: pointer;
    user-select: none;
}

.back-button {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
    margin-bottom: 16px;
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

/* ===================== Network Visualization ===================== */
.network-visualization-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -50px;
    width: 700px;
    height: 80px;
    z-index: 1;
}

.network-visualization {
    width: 700px;
    height: 80px;
    position: relative;
}

#network-canvas {
    background: transparent;
    position: relative;
    z-index: 1;
}

/* Gradient behind visualization */
.network-visualization::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.8) 100%);
    z-index: 0;
    pointer-events: none;
}

/* ===================== Subnet Detail Metrics ===================== */
.network-metrics {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin: 0;
    margin-top: 16px;
}

/* Base card styles - applied to both network and metric cards */
.network-card, .metric-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 12px 16px;
    min-height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;
}

/* Separate styling for moving price value */
#network-moving-price {
    color: #FF9900;
}

.metrics-section {
    margin-top: 0px;
    margin-bottom: 16px;
}

.metrics-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin: 0;
    margin-top: 2px;
}

.market-metrics .metric-card {
    background: rgba(255, 255, 255, 0.05);
    min-height: 70px;
}

.metric-label {
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.metric-value {
    font-size: 1.2em;
    line-height: 1.3;
    margin: 0;
}

/* Add status colors */
.registration-status {
    color: #2ECC71;
}

.registration-status.closed {
    color: #ff4444;  /* Red color for closed status */
}

.cr-status {
    color: #2ECC71;
}

.cr-status.disabled {
    color: #ff4444;  /* Red color for disabled status */
}

/* ===================== Stakes Table ===================== */
.stakes-container {
    margin-top: 24px;
    padding: 0 24px;
}

.stakes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stakes-header h3 {
    font-size: 1.2em;
    color: #ffffff;
    margin: 0;
}

.stakes-table-container {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 24px;
    width: 100%;
}

.stakes-table {
    width: 100%;
    border-collapse: collapse;
}

.stakes-table th {
    background: rgba(255, 255, 255, 0.05);
    padding: 16px;
    text-align: left;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
}

.stakes-table td {
    padding: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.stakes-table tr {
    transition: background-color 0.2s ease;
}

.stakes-table tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.02);
}

.stakes-table tr:hover {
    background: transparent;
}

.no-stakes-row td {
    text-align: center;
    padding: 32px;
    color: rgba(255, 255, 255, 0.5);
}

/* Table styles consistency */
.stakes-table th, .network-table th {
    background: rgba(255, 255, 255, 0.05);
    padding: 16px;
    text-align: left;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.2s ease;
}

/* Sortable columns */
.stakes-table th.sortable, .network-table th.sortable {
    cursor: pointer;
}

/* Active sort column - only change color */
.stakes-table th.sortable[data-sort], .network-table th.sortable[data-sort] {
    color: #FF9900;
}

/* Hover effects - only change color */
.stakes-table th.sortable:hover, .network-table th.sortable:hover {
    color: #FF9900;
}

/* Remove hover background from table rows */
.stakes-table tr:hover {
    background: transparent;
}

/* ===================== Network Table ===================== */
.network-table-container {
    margin-top: 60px;
    position: relative;
    z-index: 2;
    background: rgba(0, 0, 0, 0.8);
}

.network-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.network-table th {
    background: rgba(255, 255, 255, 0.05);
    padding: 16px;
    text-align: left;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
}

.network-table td {
    padding: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.network-table tr {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.network-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.network-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

.network-table tr:nth-child(even):hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.network-search-container {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 16px;
}

.network-search {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(255, 153, 0, 0.2);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-size: 0.95em;
    transition: all 0.2s ease;
}

.network-search:focus {
    outline: none;
    border-color: rgba(255, 153, 0, 0.5);
    background: rgba(0, 0, 0, 0.3);
    caret-color: #FF9900;
}

.network-search::placeholder {
    color: rgba(255, 255, 255, 0.3);
}

/* ===================== Cell Styles & Formatting ===================== */
.hotkey-cell {
    max-width: 200px;
    position: relative;
}

.hotkey-container {
    position: relative;
    display: inline-block;
    max-width: 100%;
}

.hotkey-identity, .truncated-address {
    color: rgba(255, 255, 255, 0.8);
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.copy-button {
    position: absolute;
    top: -20px; /* Position above the text */
    right: 0;
    background: rgba(255, 153, 0, 0.1);
    color: rgba(255, 255, 255, 0.6);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7em;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    transform: translateY(5px);
}

.hotkey-container:hover .copy-button {
    opacity: 1;
    transform: translateY(0);
}

.copy-button:hover {
    background: rgba(255, 153, 0, 0.2);
    color: #FF9900;
}

.address-cell {
    max-width: 150px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.address-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.address-container:hover::after {
    content: 'Click to copy';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 153, 0, 0.1);
    color: #FF9900;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8em;
    opacity: 0.8;
}

.truncated-address {
    font-family: monospace;
    color: rgba(255, 255, 255, 0.8);
    overflow: hidden;
    text-overflow: ellipsis;
}

.truncated-address:hover {
    color: #FF9900;
}

.registered-yes {
    color: #FF9900;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.registered-no {
    color: #ff4444;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.manage-button {
    background: rgba(255, 153, 0, 0.1);
    border: 1px solid rgba(255, 153, 0, 0.2);
    color: #FF9900;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.manage-button:hover {
    background: rgba(255, 153, 0, 0.2);
    transform: translateY(-1px);
}

.hotkey-identity {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #FF9900;
}

.identity-cell {
    max-width: 700px;
    font-size: 0.90em;
    letter-spacing: -0.2px;
    color: #FF9900;
}

.per-day {
    font-size: 0.75em;
    opacity: 0.7;
    margin-left: 4px;
}

/* ===================== Neuron Detail Panel ===================== */
#neuron-detail-container {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    padding: 16px;
    margin-top: 16px;
}

.neuron-detail-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.neuron-detail-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.neuron-info-top {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.neuron-keys {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1em;
    color: rgba(255, 255, 255, 0.7);
}

.neuron-cards-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.neuron-metrics-row {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 12px;
    margin: 0;
}

.neuron-metrics-row.last-row {
    grid-template-columns: repeat(3, 1fr);
}

/* IP Info styling */
#neuron-ipinfo {
    font-size: 0.85em;
    line-height: 1.4;
    white-space: nowrap;
}

#neuron-ipinfo .no-connection {
    color: #ff4444;
    font-weight: 500;
}

/* Adjust metric card for IP info to accommodate multiple lines */
.neuron-cards-container .metric-card:has(#neuron-ipinfo) {
    min-height: 85px;
}

/* ===================== Subnet Page Color Overrides ===================== */
/* Subnet page specific style */
.subnet-page .metric-card-title,
.subnet-page .network-card-title {
    color: rgba(255, 255, 255, 0.7);
}

.subnet-page .metric-card .metric-value,
.subnet-page .metric-value {
    color: white;
}

/* Green values */
.subnet-page .validator-true,
.subnet-page .active-yes,
.subnet-page .registration-open,
.subnet-page .cr-enabled,
.subnet-page .ip-info {
    color: #FF9900;
}

/* Red values */
.subnet-page .validator-false,
.subnet-page .active-no,
.subnet-page .registration-closed,
.subnet-page .cr-disabled,
.subnet-page .ip-na {
    color: #ff4444;
}

/* Keep symbols green in subnet page */
.subnet-page .symbol {
    color: #FF9900;
}

/* ===================== Responsive Styles ===================== */
@media (max-width: 1200px) {
    .stakes-table {
        display: block;
        overflow-x: auto;
    }

    .network-metrics {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1201px) {
    .network-metrics {
                        grid-template-columns: repeat(5, 1fr);
    }
}
/* ===== Splash Screen ===== */
#splash-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000000;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999999;
    opacity: 1;
    transition: opacity 1s ease;
}

#splash-screen.fade-out {
    opacity: 0;
}

.splash-content {
    text-align: center;
    color: #FF9900;
    opacity: 0;
    animation: fadeIn 1.2s ease forwards;
}
@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: scale(0.97);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Title & text styling */
.title-row {
    display: flex;
    align-items: baseline;
    gap: 1rem;
}

.splash-title {
    font-size: 2.4rem;
    margin: 0;
    padding: 0;
    font-weight: 600;
    color: #FF9900;
}

.beta-text {
    font-size: 0.9rem;
    color: #FF9900;
    background: rgba(255, 153, 0, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}