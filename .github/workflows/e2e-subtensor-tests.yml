name: E2E Subtensor Tests

concurrency:
  group: e2e-subtensor-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches: [main, development, staging]

  pull_request:
    branches: [main, development, staging]
    types: [ opened, synchronize, reopened, ready_for_review ]

  workflow_dispatch:
    inputs:
      verbose:
        description: "Output more information when triggered manually"
        required: false
        default: ""

env:
  CARGO_TERM_COLOR: always
  VERBOSE: ${{ github.event.inputs.verbose }}

jobs:

  find-tests:
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'pull_request' || github.event.pull_request.draft == false }}
    outputs:
      test-files: ${{ steps.get-tests.outputs.test-files }}
    steps:
      - name: Check-out repository under $GITHUB_WORKSPACE
        uses: actions/checkout@v4

      - name: Find test files
        id: get-tests
        run: |
          test_files=$(find tests/e2e_tests -name "test*.py" | jq -R -s -c 'split("\n") | map(select(. != ""))')
          echo "::set-output name=test-files::$test_files"
        shell: bash

  pull-docker-image:
    runs-on: ubuntu-latest
    steps:
      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $GITHUB_ACTOR --password-stdin

      - name: Pull Docker Image
        run: docker pull ghcr.io/opentensor/subtensor-localnet:devnet-ready

      - name: Save Docker Image to Cache
        run: docker save -o subtensor-localnet.tar ghcr.io/opentensor/subtensor-localnet:devnet-ready

      - name: Upload Docker Image as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: subtensor-localnet
          path: subtensor-localnet.tar

  run-e2e-tests:
    name: ${{ matrix.test-file }} / Python ${{ matrix.python-version }}
    needs:
      - find-tests
      - pull-docker-image
    runs-on: ubuntu-latest
    timeout-minutes: 45
    strategy:
      fail-fast: false  # Allow other matrix jobs to run even if this job fails
      max-parallel: 32  # Set the maximum number of parallel jobs (same as we have cores in SubtensorCI runner)
      matrix:
        os:
          - ubuntu-latest
        test-file: ${{ fromJson(needs.find-tests.outputs.test-files) }}
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
    steps:
      - name: Check-out repository
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v4
        with:
          python-version: 3.13

      - name: install dependencies
        run: |
          uv venv .venv
          source .venv/bin/activate
          uv pip install .[dev]

      - name: Download Cached Docker Image
        uses: actions/download-artifact@v4
        with:
          name: subtensor-localnet

      - name: Load Docker Image
        run: docker load -i subtensor-localnet.tar

      - name: Run tests
        run: |
          source .venv/bin/activate
          uv run pytest ${{ matrix.test-file }} -s
