name: Build and Publish bittensor-cli

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release'
        required: true
        type: string

jobs:
  build:
    name: Build Python distribution
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install setuptools wheel twine build toml

    - name: Build package
      run: python -m build --sdist --wheel --outdir dist/

    - name: Check if package version already exists
      run: |
        PACKAGE_NAME=$(python -c "import toml; print(toml.load('pyproject.toml')['project']['name'])") 
        PACKAGE_VERSION=${{ github.event.inputs.version }}
        if twine check dist/*; then
          if pip install $PACKAGE_NAME==$PACKAGE_VERSION; then
            echo "Error: Version $PACKAGE_VERSION of $PACKAGE_NAME already exists on PyPI"
            exit 1
          else
            echo "Version $PACKAGE_VERSION of $PACKAGE_NAME does not exist on PyPI. Proceeding with upload."
          fi
        else
          echo "Error: Twine check failed."
          exit 1
        fi

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/

  approve-and-publish:
    needs: build
    runs-on: ubuntu-latest
    environment: release
    permissions:
      contents: read
      id-token: write

    steps:
    - name: Download artifact
      uses: actions/download-artifact@v4
      with:
        name: dist
        path: dist/

    - name: Publish package distributions to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        verbose: true
        print-hash: true