# Byte-compiled / optimized / DLL files
**/__pycache__/
*.py[cod]
*$py.class
*.pyc

# Remove notebooks.
*.ipynb

# weigths and biases
wandb/

*.csv
*.torch
*.pt
*.log

# runs/data/models/logs/~
data/
**/data/

# C extensions
*.so

# IDE
*.idea/

# VSCODE
.vscode/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/
# Generated by Cargo
# will have compiled files and executables
**/target/
# These are backup files generated by rustfmt
**/*.rs.bk

# The cache for docker container dependency
.cargo

# The cache for chain data in container
.local

# State folder for all neurons.
**/data/*
!data/.gitkeep

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# PIPY Stuff
bittensor.egg-info
bittensor*.egg
bdist.*

npm-debug.log*
yarn-debug.log*
yarn-error.log*

**/build/*
**/dist/*
**/runs/*
**/env/*
**/data/*
**/.data/*
**/tmp/*

**/.bash_history
**/*.xml
**/*.pstats
**/*.png

# Replicate library
**/.replicate
replicate.yaml
**/run.sh

# Notebooks
*.ipynb
