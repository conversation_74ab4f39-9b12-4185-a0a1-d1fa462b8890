[build-system]
requires = ["setuptools>=70.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "bittensor-cli"
version = "9.10.1"
description = "Bittensor CLI"
readme = "README.md"
authors = [
    {name = "bittensor.com"}
]
license = { file = "LICENSE" }
scripts = { btcli = "bittensor_cli.cli:main" }
requires-python = ">=3.9,<3.14"
dependencies = [
    "wheel",
    "async-substrate-interface>=1.4.2",
    "aiohttp~=3.10.2",
    "backoff~=2.2.1",
    "GitPython>=3.0.0",
    "netaddr~=1.3.0",
    "numpy>=2.0.1,<3.0.0",
    "Jinja2",
    "pycryptodome>=3.0.0,<4.0.0",
    "PyYAML~=6.0.1",
    "rich>=13.7,<15.0",
    "scalecodec==1.2.11",
    "typer>=0.16",
    "bittensor-wallet>=4.0.0",
    "packaging",
    "plotille>=5.0.0",
    "plotly>=6.0.0",
]

[project.optional-dependencies]
cuda = [
    "torch>=1.13.1,<3.0",
]
dev = [
    "pytest",
    "pytest-asyncio",
    "ruff==0.11.5",
]

[project.urls]
# more details can be found here
homepage = "https://github.com/opentensor/btcli"
Repository = "https://github.com/opentensor/btcli"

[tool.flit.metadata]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Utilities"
]

[tool.setuptools]
package-dir = {"bittensor_cli" = "bittensor_cli"}
include-package-data = true
